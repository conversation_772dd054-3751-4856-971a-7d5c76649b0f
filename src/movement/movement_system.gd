class_name MovementSystem
extends Node

func _process(delta: float) -> void:
    var components: Array[Node] = get_tree().get_nodes_in_group(&"movement_components")
    for node: Node in components:
        var component: MovementComponent = node as MovementComponent
        if component == null or component.data == null or component.actor == null:
            continue

        var data: MovementData = component.data
        if not data.is_moving:
            continue

        var increment: float = delta / max(0.001, data.move_duration)
        data.progress = move_toward(data.progress, 1.0, increment)
        component.actor.position = data.start_position.lerp(data.target_position, data.progress)

        if is_equal_approx(data.progress, 1.0):
            component.actor.position = data.target_position
            data.is_moving = false
            data.progress = 0.0
            data.movement_completed.emit()

func move(component: MovementComponent, direction: Vector2) -> void:
    if component == null or component.data == null or component.actor == null or component.collision_ray == null:
        return

    var data: MovementData = component.data
    if data.is_moving:
        return

    var target_pos: Vector2 = direction * data.tile_size
    component.collision_ray.target_position = target_pos
    component.collision_ray.force_raycast_update()

    if component.collision_ray.is_colliding():
        data.movement_blocked.emit(direction)
        return

    data.start_position = component.actor.position
    data.target_position = component.actor.position + target_pos
    data.is_moving = true
    data.progress = 0.0
    data.movement_started.emit(direction)

func teleport(component: MovementComponent, global_target_pos: Vector2) -> void:
    if component == null or component.data == null or component.actor == null:
        return

    var data: MovementData = component.data
    if data.is_moving:
        return

    if component.collision_ray != null:
        var saved_mask: int = component.collision_ray.collision_mask
        component.collision_ray.collision_mask = 0

        data.movement_completed.connect(
            func() -> void:
                if is_instance_valid(component) and is_instance_valid(component.collision_ray):
                    component.collision_ray.collision_mask = saved_mask,
					CONNECT_ONE_SHOT
        )

    var local_target_pos: Vector2 = (component.actor.get_parent() as Node2D).to_local(global_target_pos)

    data.start_position = component.actor.position
    data.target_position = local_target_pos
    data.is_moving = true
    data.progress = 0.0
    data.movement_started.emit(local_target_pos - data.start_position)
